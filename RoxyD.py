import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from main import get_captcha,get_email,get_email_data,get_codeinfo,get_augtoken,add_session
from selenium.webdriver.common.action_chains import ActionChains
from RoxyClient import RoxyClient
import json
import re,os
import html
import logging
from logging.handlers import RotatingFileHandler
import string
import requests
import email
import html
import uuid
from email.utils import formatdate
s = requests.Session()


# 设置日志
def setup_logger():
    # 如果日志目录不存在则创建
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 全局清理：移除 root 和已存在 logger 的所有 handler，避免触发遗留的文件句柄
    try:
        for name in list(logging.Logger.manager.loggerDict.keys()):
            lg = logging.getLogger(name)
            for h in list(getattr(lg, 'handlers', [])):
                try:
                    h.close()
                except Exception:
                    pass
                lg.removeHandler(h)
        for h in list(logging.root.handlers):
            try:
                h.close()
            except Exception:
                pass
            logging.root.removeHandler(h)
    except Exception:
        pass

    # 配置日志器
    logger = logging.getLogger('Roxyspider')
    logger.setLevel(logging.DEBUG)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件处理器（轮转日志文件，每个文件最大5MB，保留5个备份文件）
    # 为避免 Windows 上被其他进程占用，默认使用带时间戳+PID 的独立日志文件
    unique_log = os.path.join('logs', f"spider_{time.strftime('%Y%m%d_%H%M%S')}_{os.getpid()}.log")
    file_handler = RotatingFileHandler(
        unique_log,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5,
        encoding='utf-8',      # 指定UTF-8编码
        delay=True
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志器（避免重复添加）
    logger.handlers.clear()
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 禁止向 root logger 传播，避免其他进程残留的 file handler（如 spider.log）被触发
    logger.propagate = False

    # 避免 logging 打印 handler 内部异常堆栈
    logging.raiseExceptions = False

    return logger

# 初始化日志器
logger = setup_logger()
s = requests.Session()

# 将所有 print 同步写入 logger.info
try:
    import builtins as _builtins
    _original_print = _builtins.print
    def _print_and_log(*args, **kwargs):
        sep = kwargs.get('sep', ' ')
        try:
            msg = sep.join(str(a) for a in args)
        except Exception:
            msg = sep.join(repr(a) for a in args)
        # 先原样打印到控制台
        _original_print(*args, **kwargs)
        # 再写入日志（info 级别）
        try:
            logger.info(msg)
        except Exception:
            pass
    _builtins.print = _print_and_log
except Exception:
    pass







def simulate_human_behavior(driver):
    """模拟人类行为：轻微滚动、随机停顿、页面交互、鼠标移动"""
    try:
        # 1. 模拟页面加载后的自然停顿
        initial_pause = random.uniform(0.8, 1.5)
        time.sleep(initial_pause)

        # 2. 随机轻微滚动（模拟用户查看页面）
        try:
            scroll_amount = random.randint(-150, 150)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            print(f"已执行拟人滚动: {scroll_amount}px")
        except Exception as scroll_error:
            print(f"滚动操作跳过: {scroll_error}")

        # 3. 模拟鼠标随机移动
        try:
            actions = ActionChains(driver)
            # 获取窗口大小
            window_size = driver.get_window_size()
            width, height = window_size['width'], window_size['height']

            # 随机移动鼠标到页面上的某个位置
            random_x = random.randint(100, width - 100)
            random_y = random.randint(100, height - 100)
            actions.move_by_offset(random_x - width//2, random_y - height//2)
            actions.perform()
            print(f"已执行鼠标移动: ({random_x}, {random_y})")
        except Exception as mouse_error:
            print(f"鼠标移动操作跳过: {mouse_error}")

        # 4. 短暂停顿
        pause_time = random.uniform(0.6, 1.2)
        time.sleep(pause_time)

        # 5. 模拟页面焦点变化和键盘事件
        try:
            # 随机执行一些页面交互
            interaction_type = random.choice(['focus', 'tab', 'scroll_small'])

            if interaction_type == 'focus':
                driver.execute_script("document.body.focus();")
                print("已执行页面焦点操作")
            elif interaction_type == 'tab':
                # 模拟按Tab键（但不实际按下）
                driver.execute_script("document.activeElement && document.activeElement.blur();")
                print("已执行焦点模糊操作")
            elif interaction_type == 'scroll_small':
                small_scroll = random.randint(-50, 50)
                driver.execute_script(f"window.scrollBy(0, {small_scroll});")
                print(f"已执行小幅滚动: {small_scroll}px")

        except Exception as focus_error:
            print(f"交互操作跳过: {focus_error}")

        # 6. 模拟阅读时间
        reading_time = random.uniform(0.5, 1.0)
        time.sleep(reading_time)

        # 7. 再次随机停顿
        final_pause = random.uniform(0.3, 0.8)
        time.sleep(final_pause)

        total_time = initial_pause + pause_time + reading_time + final_pause
        print(f"已执行拟人操作完成，总停顿: {total_time:.2f}秒")

    except Exception as e:
        print(f"拟人操作部分失败，继续执行: {e}")
        # 即使失败也要有基本的停顿
        time.sleep(random.uniform(0.5, 1.0))




def setup_cdp_anti_detection(driver):
    """通过 CDP 设置高级反检测机制"""
    try:
        # 1) 在新文档注入高级反检测脚本
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
// ===== 核心反检测 =====
// 完全隐藏 webdriver 属性
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
    configurable: true,
    enumerable: false
});

// 移除所有自动化相关的全局变量
const automationVars = [
    'cdc_adoQpoasnfa76pfcZLmcfl_Array',
    'cdc_adoQpoasnfa76pfcZLmcfl_Promise',
    'cdc_adoQpoasnfa76pfcZLmcfl_Symbol',
    'cdc_adoQpoasnfa76pfcZLmcfl_JSON',
    'cdc_adoQpoasnfa76pfcZLmcfl_Object',
    'cdc_adoQpoasnfa76pfcZLmcfl_Proxy',
    '$cdc_asdjflasutopfhvcZLmcfl_',
    '$chrome_asyncScriptInfo'
];

automationVars.forEach(varName => {
    try {
        delete window[varName];
        if (document) delete document[varName];
    } catch(e) {}
});

// ===== 浏览器指纹伪装 =====
// 语言设置
Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
    configurable: true
});

// 平台信息
Object.defineProperty(navigator, 'platform', {
    get: () => 'Win32',
    configurable: true
});

// 硬件信息
Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => 8,
    configurable: true
});

Object.defineProperty(navigator, 'deviceMemory', {
    get: () => 8,
    configurable: true
});

Object.defineProperty(navigator, 'maxTouchPoints', {
    get: () => 0,
    configurable: true
});

// 网络连接信息
if (navigator.connection) {
    Object.defineProperty(navigator.connection, 'downlink', {
        get: () => 10,
        configurable: true
    });
    Object.defineProperty(navigator.connection, 'effectiveType', {
        get: () => '4g',
        configurable: true
    });
    Object.defineProperty(navigator.connection, 'rtt', {
        get: () => 50,
        configurable: true
    });
}

// ===== 插件伪装 =====
Object.defineProperty(navigator, 'plugins', {
    get: () => ({
        length: 3,
        0: {
            name: 'Chrome PDF Plugin',
            filename: 'internal-pdf-viewer',
            description: 'Portable Document Format'
        },
        1: {
            name: 'Chrome PDF Viewer',
            filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
            description: ''
        },
        2: {
            name: 'Native Client',
            filename: 'internal-nacl-plugin',
            description: ''
        }
    }),
    configurable: true
});

// ===== 权限API伪装 =====
if (navigator.permissions && navigator.permissions.query) {
    const originalQuery = navigator.permissions.query;
    navigator.permissions.query = function(parameters) {
        return parameters && parameters.name === 'notifications'
            ? Promise.resolve({ state: Notification.permission })
            : originalQuery.call(this, parameters);
    };
}

// ===== WebGL指纹伪装 =====
const getParameter = WebGLRenderingContext && WebGLRenderingContext.prototype.getParameter;
if (getParameter) {
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) return 'Intel Inc.';
        if (parameter === 37446) return 'Intel(R) HD Graphics 630';
        if (parameter === 7936) return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
        if (parameter === 7937) return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
        return getParameter.call(this, parameter);
    };
}

// ===== Chrome对象伪装 =====
if (!window.chrome) {
    window.chrome = {
        runtime: {
            onConnect: undefined,
            onMessage: undefined
        },
        loadTimes: function() {
            return {
                requestTime: Date.now() / 1000 - Math.random(),
                startLoadTime: Date.now() / 1000 - Math.random(),
                commitLoadTime: Date.now() / 1000 - Math.random(),
                finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                finishLoadTime: Date.now() / 1000 - Math.random(),
                firstPaintTime: Date.now() / 1000 - Math.random(),
                firstPaintAfterLoadTime: 0,
                navigationType: 'Other',
                wasFetchedViaSpdy: false,
                wasNpnNegotiated: false,
                npnNegotiatedProtocol: 'unknown',
                wasAlternateProtocolAvailable: false,
                connectionInfo: 'http/1.1'
            };
        },
        csi: function() {
            return {
                startE: Date.now(),
                onloadT: Date.now(),
                pageT: Math.random() * 1000,
                tran: 15
            };
        },
        app: {
            isInstalled: false
        }
    };
}

// ===== 屏幕信息伪装 =====
Object.defineProperty(screen, 'availWidth', { get: () => 1920, configurable: true });
Object.defineProperty(screen, 'availHeight', { get: () => 1040, configurable: true });
Object.defineProperty(screen, 'width', { get: () => 1920, configurable: true });
Object.defineProperty(screen, 'height', { get: () => 1080, configurable: true });
Object.defineProperty(screen, 'colorDepth', { get: () => 24, configurable: true });
Object.defineProperty(screen, 'pixelDepth', { get: () => 24, configurable: true });

// ===== 时区伪装 =====
try {
    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
    Intl.DateTimeFormat.prototype.resolvedOptions = function() {
        const options = originalResolvedOptions.call(this);
        options.timeZone = 'America/New_York';
        return options;
    };
} catch(e) {}

// ===== 鼠标事件伪装 =====
let mouseEventCount = 0;
['mousedown', 'mouseup', 'mousemove', 'click'].forEach(eventType => {
    document.addEventListener(eventType, () => {
        mouseEventCount++;
    }, true);
});

// ===== 键盘事件伪装 =====
let keyEventCount = 0;
['keydown', 'keyup', 'keypress'].forEach(eventType => {
    document.addEventListener(eventType, () => {
        keyEventCount++;
    }, true);
});

// ===== 触摸事件伪装 =====
Object.defineProperty(navigator, 'maxTouchPoints', {
    get: () => 0,
    configurable: true
});

// ===== 电池API伪装 =====
if (navigator.getBattery) {
    navigator.getBattery = () => Promise.resolve({
        charging: true,
        chargingTime: 0,
        dischargingTime: Infinity,
        level: 1
    });
}

// ===== 媒体设备伪装 =====
if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
    const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;
    navigator.mediaDevices.enumerateDevices = function() {
        return originalEnumerateDevices.call(this).then(devices => {
            return devices.map(device => ({
                ...device,
                label: device.label || 'Default Device'
            }));
        });
    };
}

console.log('高级反检测脚本已加载');
"""
        })

        # 2) 启用高级网络设置
        driver.execute_cdp_cmd("Network.enable", {})
        driver.execute_cdp_cmd("Runtime.enable", {})
        driver.execute_cdp_cmd("Page.enable", {})

        # 获取并修改User-Agent
        ua = driver.execute_script("return navigator.userAgent") or ""
        if "Headless" in ua:
            ua = ua.replace("Headless", "").strip()
        # 移除自动化标识
        ua = ua.replace("HeadlessChrome", "Chrome")

        driver.execute_cdp_cmd("Network.setUserAgentOverride", {
            "userAgent": ua,
            "platform": "Win32",
            "acceptLanguage": "en-US,en;q=0.9"
        })

        # 设置真实的HTTP请求头
        driver.execute_cdp_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "max-age=0",
                "Sec-Ch-Ua": '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": ua
            }
        })

        # 3) 高级模拟设置
        try:
            # 时区设置
            driver.execute_cdp_cmd("Emulation.setTimezoneOverride", {
                "timezoneId": "America/New_York"
            })
        except Exception:
            pass

        try:
            # 绕过CSP
            driver.execute_cdp_cmd("Page.setBypassCSP", {"enabled": True})
        except Exception:
            pass

        try:
            # 地理位置伪装
            driver.execute_cdp_cmd("Emulation.setGeolocationOverride", {
                "latitude": 40.7589,
                "longitude": -73.9851,
                "accuracy": 100
            })
        except Exception:
            pass

        try:
            # 设备指标伪装
            driver.execute_cdp_cmd("Emulation.setDeviceMetricsOverride", {
                "width": 1920,
                "height": 1080,
                "deviceScaleFactor": 1,
                "mobile": False
            })
        except Exception:
            pass

        try:
            # 媒体特性伪装
            driver.execute_cdp_cmd("Emulation.setEmulatedMedia", {
                "media": "screen"
            })
        except Exception:
            pass

        # 4) 启用网络监控
        try:
            setup_network_monitoring(driver)
        except Exception as e:
            print(f"网络监控设置失败: {e}")

        print("高级CDP反检测机制设置完成")

    except Exception as e:
        print(f"CDP 反检测设置失败: {e}")


def setup_driver():
    """初始化并返回一个 Chrome WebDriver 实例。"""
    # 这里可以添加更多的 WebDriver 配置，例如无头模式等
    # options = webdriver.ChromeOptions()
    # options.add_argument('--headless')
    # driver = webdriver.Chrome(options=options)
    brwoser_id = os.getenv("ROXYBRWOSER_ID","")
    workspaceid = int(os.getenv("ROXYWORK_ID"))
    # 初始化客户端
    client = RoxyClient(port=50000,token=os.getenv("ROXYTOKEN"))
    client.browser_local_cache([brwoser_id])
    client.browser_server_cache(workspaceid,[brwoser_id])
    client.browser_random_env(workspaceid,brwoser_id)
    # 打开浏览器
    rsp = client.browser_open(brwoser_id)
    if rsp.get("code") != 0:
        print("浏览器打开失败:",rsp)
        exit(0)
    # 获取selenium的连接信息
    debuggerAddress = rsp.get("data").get("http")
    driverPath = rsp.get("data").get("driver")
    print(f"浏览器打开成功,debuggerAddress:{debuggerAddress},driverPath:{driverPath}")

    # selenium 连接代码
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("debuggerAddress", debuggerAddress)

    print(debuggerAddress)
    chrome_service = Service(driverPath)
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
    #driver = webdriver.Chrome()
    driver.implicitly_wait(10) # 设置一个隐式等待


    return driver


def apply_anti_detection(driver):
    """在现有driver上应用高级反检测机制，不修改setup_driver函数"""
    try:
        print("正在应用高级反检测机制...")

        # 1. 设置CDP反检测
        setup_cdp_anti_detection(driver)

        # 2. 启用高级隐身模式
        advanced_stealth_mode(driver)

        # 3. 额外的反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # 4. 模拟真实用户环境
        simulate_real_user_environment(driver)

        print("✅ 高级反检测机制应用完成")
        return True

    except Exception as e:
        print(f"❌ 应用反检测机制失败: {e}")
        return False


def simulate_real_user_environment(driver):
    """模拟真实用户环境"""
    try:
        # 设置真实的窗口大小
        driver.set_window_size(1920, 1080)

        # 模拟用户已经使用浏览器一段时间
        driver.execute_script("""
            // 模拟用户活动痕迹
            window._userActivitySimulated = true;

            // 模拟localStorage中的数据
            try {
                localStorage.setItem('_user_session_start', Date.now() - Math.random() * 3600000);
                localStorage.setItem('_page_visits', Math.floor(Math.random() * 50) + 10);
            } catch(e) {}

            // 模拟sessionStorage中的数据
            try {
                sessionStorage.setItem('_tab_opened', Date.now() - Math.random() * 1800000);
            } catch(e) {}

            // 模拟滚动历史
            window._scrollHistory = [];
            for (let i = 0; i < 10; i++) {
                window._scrollHistory.push({
                    y: Math.random() * 1000,
                    timestamp: Date.now() - Math.random() * 300000
                });
            }

            // 模拟点击历史
            window._clickHistory = [];
            for (let i = 0; i < 15; i++) {
                window._clickHistory.push({
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    timestamp: Date.now() - Math.random() * 600000
                });
            }
        """)

        print("真实用户环境模拟完成")
        return True

    except Exception as e:
        print(f"真实用户环境模拟失败: {e}")
        return False


def setup_network_monitoring(driver):
    """设置网络监控，检测可疑的检测请求"""
    try:
        # 启用网络域监控
        driver.execute_cdp_cmd("Network.enable", {})

        # 监控请求拦截
        def handle_request(message):
            try:
                if message.get('method') == 'Network.requestWillBeSent':
                    request = message.get('params', {}).get('request', {})
                    url = request.get('url', '')

                    # 检测可疑的反爬虫检测请求
                    suspicious_patterns = [
                        'bot-detection',
                        'anti-bot',
                        'captcha',
                        'challenge',
                        'verification',
                        'fingerprint',
                        'detection'
                    ]

                    for pattern in suspicious_patterns:
                        if pattern in url.lower():
                            print(f"⚠️ 检测到可疑请求: {url}")
                            break

            except Exception as e:
                pass

        print("网络监控已启用")
        return True

    except Exception as e:
        print(f"网络监控设置失败: {e}")
        return False


def advanced_stealth_mode(driver):
    """启用高级隐身模式"""
    try:
        # 执行更深层的反检测脚本
        driver.execute_script("""
            // 高级反检测 - 运行时注入

            // 1. 移除所有可能的自动化标识
            const removeAutomationTraces = () => {
                // 删除所有CDC相关变量
                Object.getOwnPropertyNames(window).forEach(prop => {
                    if (prop.includes('cdc_') || prop.includes('$cdc_') || prop.includes('chrome_')) {
                        try {
                            delete window[prop];
                        } catch(e) {}
                    }
                });

                // 删除document上的自动化属性
                if (document) {
                    Object.getOwnPropertyNames(document).forEach(prop => {
                        if (prop.includes('cdc_') || prop.includes('$cdc_') || prop.includes('webdriver')) {
                            try {
                                delete document[prop];
                            } catch(e) {}
                        }
                    });
                }
            };

            // 2. 伪造鼠标和键盘活动历史
            const simulateUserActivity = () => {
                // 模拟鼠标移动历史
                window._mouseHistory = [];
                for (let i = 0; i < 50; i++) {
                    window._mouseHistory.push({
                        x: Math.random() * window.innerWidth,
                        y: Math.random() * window.innerHeight,
                        timestamp: Date.now() - Math.random() * 60000
                    });
                }

                // 模拟键盘活动历史
                window._keyHistory = [];
                for (let i = 0; i < 20; i++) {
                    window._keyHistory.push({
                        key: String.fromCharCode(65 + Math.floor(Math.random() * 26)),
                        timestamp: Date.now() - Math.random() * 60000
                    });
                }
            };

            // 3. 伪造浏览器历史
            const fakeBrowserHistory = () => {
                try {
                    // 添加一些假的历史记录
                    const fakeUrls = [
                        'https://www.google.com',
                        'https://github.com',
                        'https://stackoverflow.com'
                    ];

                    // 注意：实际上无法修改history.length，但可以创建假的属性
                    Object.defineProperty(window, '_visitHistory', {
                        value: fakeUrls,
                        writable: false
                    });
                } catch(e) {}
            };

            // 4. 伪造性能指标
            const fakePerformanceMetrics = () => {
                if (window.performance && window.performance.timing) {
                    const now = Date.now();
                    const fakeTimings = {
                        navigationStart: now - 3000,
                        domainLookupStart: now - 2800,
                        domainLookupEnd: now - 2700,
                        connectStart: now - 2600,
                        connectEnd: now - 2400,
                        requestStart: now - 2000,
                        responseStart: now - 1500,
                        responseEnd: now - 1000,
                        domLoading: now - 800,
                        domContentLoadedEventStart: now - 400,
                        domContentLoadedEventEnd: now - 300,
                        loadEventStart: now - 100,
                        loadEventEnd: now
                    };

                    Object.keys(fakeTimings).forEach(key => {
                        try {
                            Object.defineProperty(window.performance.timing, key, {
                                get: () => fakeTimings[key],
                                configurable: true
                            });
                        } catch(e) {}
                    });
                }
            };

            // 5. 伪造Canvas指纹
            const fakeCanvasFingerprint = () => {
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                HTMLCanvasElement.prototype.toDataURL = function() {
                    // 添加随机噪声
                    const ctx = this.getContext('2d');
                    if (ctx) {
                        const imageData = ctx.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                            imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                            imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                        }
                        ctx.putImageData(imageData, 0, 0);
                    }
                    return originalToDataURL.apply(this, arguments);
                };
            };

            // 执行所有反检测措施
            removeAutomationTraces();
            simulateUserActivity();
            fakeBrowserHistory();
            fakePerformanceMetrics();
            fakeCanvasFingerprint();

            // 定期清理自动化痕迹
            setInterval(removeAutomationTraces, 1000);

            console.log('高级隐身模式已启用');
        """)

        print("高级隐身模式已启用")
        return True

    except Exception as e:
        print(f"高级隐身模式启用失败: {e}")
        return False


def get_captcha(url,retry=0):
    baseurl = os.getenv("CAPTCHA_URL") # "http://192.168.2.13:5000"
    def _getcap_value(id):
        cap_value = s.get(baseurl + "/result",params={"id":id})
        try:
            if cap_value.text != "CAPTCHA_NOT_READY":
                return cap_value.json()["value"]
            else:
                time.sleep(5)
                #print(f"{get_current_method_name()} --获取 cap_value 失败，重试...")
                return _getcap_value(id)
        except Exception as e:
            #print(f"{get_current_method_name()}发生错误: ====== {cap_value.text}")
            return _getcap_value(id)


    task_id = s.get(baseurl + "/turnstile",params={"url":url,"sitekey":"0x4AAAAAAAQFNSW6xordsuIq"}).json()["task_id"]

    if task_id:
        cap_value = _getcap_value(task_id)
        # print(cap_value)
        if cap_value != "CAPTCHA_FAIL":
            print(f"获取 cap_value 验证码成功...")
            return cap_value
        else:
            print(f"获取验证码失败，重试次数: {retry+1}")
            return get_captcha(url,retry+1)




def get_email():
    #SWITCH_EMAIL = rando你是什么模型你是m.choice(list(ALLDOMAIN.keys()))
    baseurl = "https://es.slogo.eu.org"
    #s.proxies = {"http": "http://127.0.0.1:10808", "https": "http://127.0.0.1:10808"}
    def _generate_random_name():
        # 生成5位英文字符
        letters1 = ''.join(random.choices(string.ascii_lowercase, k=2))
        # 生成1-3个数字
        numbers = ''.join(random.choices(string.digits, k=random.randint(1, 3)))
        # 生成1-3个英文字符
        letters2 = ''.join(random.choices(string.ascii_lowercase, k=random.randint(1, 3)))
        # 组合成最终名称
        return letters1 + str(int(time.time()))[6:]

    def _fetch_email_data(name):
        try:
            res = requests.post(
                baseurl+"/admin/new_address",
                json={
                    "enablePrefix": True,
                    "name": name,
                    "domain": DOMAIN
                    #"domain": "smartdocker.online",
                },
                headers={
                    'x-admin-auth': "chen1234.",
                    "Content-Type": "application/json"
                },
                proxies={"http": "http://***********:10808", "https": "http://***********:10808"}
            )
            if res.status_code == 200:
                response_data = res.json()
                email = response_data.get("address", 0)
                jwt = response_data.get("jwt", 0)
                return email,jwt
            else:
                print(f"请求失败，状态码: {res.status_code}")
                return None
        except requests.RequestException as e:
            print(f"请求出现错误: {e}")
            return None
    return _fetch_email_data(_generate_random_name())

def is_time_within_range(email_date_str, target_date_str, minutes=1):
    """
    检查邮件日期是否在目标时间的指定分钟范围内

    参数:
        email_date_str: 邮件中的日期字符串，格式如 "Thu, 3 Jul 2025 04:10:52 +0000"
        target_date_str: 目标日期字符串，相同格式
        minutes: 允许的时间差（分钟）

    返回:
        布尔值，表示是否在时间范围内
    """
    from email.utils import parsedate_to_datetime
    from datetime import datetime, timedelta

    try:
        # 解析邮件日期字符串为datetime对象
        email_date = parsedate_to_datetime(email_date_str)

        # 解析目标日期字符串为datetime对象
        target_date = parsedate_to_datetime(target_date_str)

        # 计算时间差（绝对值）
        time_diff = abs((email_date - target_date).total_seconds())

        # 检查时间差是否在允许范围内（转换为秒）
        return time_diff <= (minutes * 60)
    except Exception as e:
        print(f"日期比较出错: {e}")
        return False

def extract_email_elements(email_message_str):
    code = None
    # 将字符串转换为字节类型（如果原始字符串不是字节类型的话）
    email_message_bytes = email_message_str.encode('utf-8')

    # 使用BytesParser解析字节字符串为Message对象
    msg = email.message_from_bytes(email_message_bytes)

    # 访问邮件的各个部分
    if "<EMAIL>" in msg['From']:


        # 检查邮件日期是否在当前时间附近1分钟内
        target_time = formatdate(localtime=False, usegmt=True)  # 获取当前时间，格式如 "Thu, 3 Jul 2025 04:10:52 GMT"
        if is_time_within_range(msg['Date'], target_time, minutes=2):
            # 访问邮件正文
            if msg.is_multipart():
                for part in msg.walk():
                    # 每个part都是一个Message对象，我们可以检查其内容类型
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))

                    # 打印文本内容
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        body = part.get_payload(decode=True).decode()  # 解码并获取文本内容
                        match = re.search(r'Your verification code is: (\d{6})', body)
                        if match:
                            code = match.group(1)
        else:
            pass
            print(f'{msg['Date']} 时间不在最近 {target_time }')




    return code

def get_email_data(jwt):
    baseurl = "https://es.slogo.eu.org"
    code = None
    if jwt:

        res = s.get(
            baseurl+"/api/mails",
            params={"limit":10,"offset":0},
            proxies={"http": "http://***********:10808", "https": "http://***********:10808"},
            headers={
                "Authorization": f"Bearer {jwt}",
                # "x-custom-auth": "<你的网站密码>", # 如果启用了自定义密码
                "Content-Type": "application/json"
        })

        results = res.json().get("results", 0)
        if results:
            raw = results[0].get("raw", 0)
            #print(f"{get_current_method_name()} -- results -- success")
            code = extract_email_elements(raw)
        else:
            #print(f"{get_current_method_name()} -- no results")
            return get_email_data(jwt)

    return code


def get_augtoken(url,code_verifier):

    from urllib.parse import urlparse, parse_qs
    _query_str = urlparse(url).query  # 提取 ? 后面的部分
    query_str = _query_str.replace("&amp;", "&")
    # 解析参数并转为字典（重复 key 会变成数组）
    params = parse_qs(query_str)
    tenant_url = params.get("tenant_url",0)[0]
    code = params.get("code",0)[0]
    data = {}
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Augment.vscode-augment/0.526.0 (win32; x64; 10.0.19045) vscode/1.95.3",
        "x-request-id": str(uuid.uuid4()),
        "x-request-session-id": str(uuid.uuid4()),
        "x-api-version": "2"
    }
    body = {
        "grant_type": "authorization_code",
        "client_id": "augment-vscode-extension",
        "code_verifier": code_verifier,
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "code": code
    }
    res = s.post(tenant_url+"token",headers=headers,json=body).json()
    access_token = res.get("access_token", 0)
    if not access_token:
        return None
    data["accessToken"] = access_token
    headers["Authorization"] = f"Bearer {access_token}"
    res_info = s.post(tenant_url+"subscription-info",headers=headers,json={}).json()
    end_date = res_info.get("subscription",0).get("ActiveSubscription",0).get("end_date", 0)
    if not end_date:
        return None
    data["end_date"] = end_date
    data["tenantURL"] = tenant_url

    return data


def add_session(email,augmentSession:dict,expire_time="",other=""):
    data = {
            "email":email,
            "augmentSession":augmentSession,
            "expire_time":expire_time,
            "other":other
        }
    res =s.post("https://aug.202578.xyz/add_session",
                 headers={"X-User-ID": "admin"},
                 json=data
                 )
    if res.status_code == 200:
        print(f"添加session成功: {res.json()}")
    else:
        print(f"添加session失败: {res.text}")

def wait_for_page_load(driver, timeout=30):
    """等待页面完全加载并执行反检测"""
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )

        # 页面加载完成后执行额外的反检测脚本
        enhance_anti_detection(driver)

    except TimeoutException:
        print(f"页面在{timeout}秒内未完全加载")


def enhance_anti_detection(driver):
    """在页面加载后执行额外的反检测措施"""
    try:
        # 执行更多反检测脚本
        driver.execute_script("""
            // 移除自动化相关的全局变量
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;

            // 伪造更多浏览器特征
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });

            // 伪造插件信息
            Object.defineProperty(navigator, 'plugins', {
                get: () => ({
                    length: 3,
                    0: { name: 'Chrome PDF Plugin' },
                    1: { name: 'Chrome PDF Viewer' },
                    2: { name: 'Native Client' }
                })
            });

            // 伪造语言信息
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });

            // 伪造内存信息
            if ('deviceMemory' in navigator) {
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8
                });
            }

            // 伪造并发数
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8
            });

            // 移除 Selenium 相关属性
            if (window.document) {
                delete window.document.$cdc_asdjflasutopfhvcZLmcfl_;
                delete window.document.$chrome_asyncScriptInfo;
            }

            // 伪造 permissions API
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(parameters) {
                    return parameters.name === 'notifications'
                        ? Promise.resolve({ state: Notification.permission })
                        : originalQuery(parameters);
                };
            }
        """)

        print("页面反检测增强完成")

    except Exception as e:
        print(f"页面反检测增强失败: {e}")


def check_detection_status(driver):
    """检查是否被反爬虫系统检测，区分严重阻止和正常验证码"""
    try:
        # 严重阻止关键词（这些会导致直接失败）
        serious_keywords = [
            'blocked', 'forbidden', 'access denied', 'bot detected',
            'automated traffic', 'suspicious activity'
        ]

        # 正常验证关键词（这些是正常流程的一部分）
        normal_verification_keywords = [
            'verification required', 'please verify', 'captcha', 'challenge'
        ]

        # 检查页面标题
        try:
            title = driver.title.lower()
            for keyword in serious_keywords:
                if keyword in title:
                    print(f"在页面标题中检测到严重阻止: {keyword}")
                    return True
            for keyword in normal_verification_keywords:
                if keyword in title:
                    print(f"在页面标题中检测到正常验证: {keyword}")
                    return "verification"
        except:
            pass

        # 检查页面源码
        try:
            page_source = driver.page_source.lower()
            for keyword in serious_keywords:
                if keyword in page_source:
                    print(f"在页面内容中检测到严重阻止: {keyword}")
                    return True
            for keyword in normal_verification_keywords:
                if keyword in page_source:
                    print(f"在页面内容中检测到正常验证: {keyword}")
                    return "verification"
        except:
            pass

        # 检查当前URL
        try:
            current_url = driver.current_url.lower()
            blocked_url_patterns = [
                'blocked', 'denied', 'error', 'captcha', 'challenge'
            ]
            for pattern in blocked_url_patterns:
                if pattern in current_url:
                    print(f"在URL中检测到阻止模式: {pattern}")
                    return True
        except:
            pass

        # 检查是否有验证码元素
        try:
            captcha_selectors = [
                'iframe[src*="captcha"]',
                'iframe[src*="recaptcha"]',
                'div[class*="captcha"]',
                'div[id*="captcha"]',
                '.cf-challenge-form',
                '#challenge-form'
            ]

            for selector in captcha_selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"检测到验证码元素: {selector}")
                    return True
        except:
            pass

        # 检查JavaScript错误或异常
        try:
            js_errors = driver.execute_script("""
                return window.jsErrors || [];
            """)
            if js_errors:
                print(f"检测到JavaScript错误: {js_errors}")
                return True
        except:
            pass

        return False

    except Exception as e:
        print(f"检测状态检查失败: {e}")
        return False


def human_like_typing(element, text):
    """模拟人类打字，包含更真实的打字模式"""
    # 先清空输入框（模拟人类习惯）
    element.clear()
    time.sleep(random.uniform(0.1, 0.3))

    # 模拟点击输入框获得焦点
    element.click()
    time.sleep(random.uniform(0.1, 0.2))

    for i, char in enumerate(text):
        # 模拟不同的打字速度
        if i == 0:
            # 第一个字符稍慢
            delay = random.uniform(0.08, 0.15)
        elif char in ['.', '@', '-', '_']:
            # 特殊字符稍慢
            delay = random.uniform(0.06, 0.12)
        elif i > 0 and text[i-1] == char:
            # 连续相同字符稍快
            delay = random.uniform(0.02, 0.06)
        else:
            # 正常字符
            delay = random.uniform(0.03, 0.08)

        element.send_keys(char)
        time.sleep(delay)

        # 偶尔模拟打字停顿（思考时间）
        if random.random() < 0.1:  # 10% 概率
            time.sleep(random.uniform(0.2, 0.5))

def human_like_mouse_move_and_click(driver, element):
    """
    模拟人类的鼠标移动轨迹和点击，包含更真实的移动模式
    """
    actions = ActionChains(driver)

    try:
        # 获取元素位置和大小
        element_rect = element.rect
        element_x = element_rect['x'] + element_rect['width'] // 2
        element_y = element_rect['y'] + element_rect['height'] // 2

        # 步骤 1: 先移动到元素附近的随机位置（模拟寻找目标）
        approach_offset_x = random.randint(-80, 80)
        approach_offset_y = random.randint(-80, 80)
        actions.move_to_element_with_offset(element, approach_offset_x, approach_offset_y)
        actions.pause(random.uniform(0.3, 0.6))

        # 步骤 2: 进行中间调整移动（模拟瞄准过程）
        mid_offset_x = random.randint(-30, 30)
        mid_offset_y = random.randint(-30, 30)
        actions.move_to_element_with_offset(element, mid_offset_x, mid_offset_y)
        actions.pause(random.uniform(0.1, 0.3))

        # 步骤 3: 精准移动到元素中心（稍有偏移，模拟人类不完美点击）
        final_offset_x = random.randint(-5, 5)
        final_offset_y = random.randint(-5, 5)
        actions.move_to_element_with_offset(element, final_offset_x, final_offset_y)
        actions.pause(random.uniform(0.05, 0.15))

        # 步骤 4: 点击前的短暂停顿
        actions.pause(random.uniform(0.05, 0.1))

        # 步骤 5: 执行点击
        actions.click()

        # 步骤 6: 点击后稍作停留（模拟人类确认点击）
        actions.pause(random.uniform(0.1, 0.2))

        # 执行所有链式操作
        actions.perform()

        print(f"已执行人性化鼠标点击，目标位置: ({element_x}, {element_y})")

    except Exception as e:
        print(f"人性化鼠标点击失败，使用备用方案: {e}")
        # 备用方案：简单点击
        try:
            actions = ActionChains(driver)
            actions.move_to_element(element).pause(0.2).click().perform()
        except:
            element.click()  # 最后的备用方案

def wait_for_human_verification(driver, timeout=60):
    """
    使用显式等待，持续检测页面是否包含“Verifying you are human”字符串。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    timeout (int): 最长等待时间（秒）。

    返回值:
    bool: 如果在超时时间内检测到字符串，则返回 True；否则返回 False。
    """
    print(f"正在等待页面出现 'Verifying you are human' 字符串，最长等待时间：{timeout} 秒...")

    try:
        # 定义一个预期条件：检查页面源代码是否包含特定字符串
        wait = WebDriverWait(driver, timeout)
        wait.until(lambda d: "Verifying you are human" in d.page_source)

        print("检测到 'Verifying you are human' 字符串！")
        return True

    except Exception as e:
        print(f"在 {timeout} 秒内未检测到 'Verifying you are human' 字符串。")
        return False


def login(driver, username):
    """
    执行登录步骤。
    1. 打开登录页面
    2. 输入用户名
    3. 点击登录按钮
    """
    print("步骤 1 & 2: 正在打开登录页面并输入用户名...")
    driver.get("https://app.augmentcode.com/account")

    # 等待页面完全加载并执行反检测
    wait_for_page_load(driver)

    # 再次应用反检测（页面加载后可能需要重新应用）
    enhance_anti_detection(driver)

    # 检查是否被检测（但不因为captcha而直接失败）
    detection_result = check_detection_status(driver)
    if detection_result and "captcha" not in driver.page_source.lower():
        print("⚠️ 检测到严重的反爬虫机制")
        return False
    elif detection_result:
        print("⚠️ 检测到验证码，将继续使用原有流程处理")

    # 模拟页面加载后的人类行为
    simulate_human_behavior(driver)

    try:
        # 等待用户名输入框加载完成并输入
        username_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "username"))
        )

        # 模拟用户查看页面的行为
        time.sleep(random.uniform(1.0, 2.0))

        # 获取验证码
        capvalue = get_captcha(driver.current_url)
        print(f"获取到验证码: {capvalue}")

        captchainbpx = driver.find_element(By.NAME, 'captcha')

        # 模拟用户先查看输入框再输入
        username_input.click()
        time.sleep(random.uniform(0.3, 0.6))

        # 人性化输入用户名
        human_like_typing(username_input, username)
        print(f"已输入用户名: {username}")

        # 模拟用户输入完成后的停顿
        time.sleep(random.uniform(0.5, 1.0))

        # 找到并点击登录按钮
        login_button = driver.find_element(By.NAME, "action")

        if captchainbpx and login_button:
            print(f"找到 Turnstile 验证码框...")

            # 注入验证码
            driver.execute_script("arguments[0].value = '{}';".format(capvalue), captchainbpx)
            print("验证码已注入")

            # 模拟用户检查输入后再点击的行为
            time.sleep(random.uniform(1.0, 2.0))

            # 人性化点击登录按钮
            human_like_mouse_move_and_click(driver, login_button)
            print("登录信息已提交，等待验证页面...")

            # 提交后的等待
            time.sleep(random.uniform(2.0, 3.0))
            return True
        return False

    except TimeoutException:
        print("错误：登录页面加载超时或找不到登录元素。")
        return False
    except Exception as e:
        print(f"登录过程中发生错误: {e}")
        return False

def verify_email(driver, email_code):
    """
    执行邮箱验证步骤。
    1. 输入验证码
    2. 点击验证按钮
    """
    print("步骤 3 & 4: 正在输入邮箱验证码...")

    # 模拟页面加载后的人类行为
    simulate_human_behavior(driver)

    try:
        # 等待验证码输入框加载完成并输入
        code_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "code"))
        )

        # 模拟用户查看页面和验证码的行为
        time.sleep(random.uniform(1.0, 2.0))
        print(f"准备输入验证码: {email_code}")

        # 人性化输入验证码
        human_like_typing(code_input, email_code)
        print("验证码输入完成")

        # 模拟用户输入完成后检查的停顿
        time.sleep(random.uniform(1.0, 2.0))

        # 找到并点击验证按钮
        verify_button = driver.find_element(By.NAME, "action")

        # 模拟用户确认输入正确后再点击的行为
        time.sleep(random.uniform(0.5, 1.0))

        # 人性化点击验证按钮
        human_like_mouse_move_and_click(driver, verify_button)
        print("验证码已提交，等待跳转...")

        # 提交后等待
        time.sleep(random.uniform(2.0, 3.0))

        return True
    except TimeoutException:
        print("错误：验证页面加载超时或找不到验证元素。")
        return False
    except Exception as e:
        print(f"邮箱验证过程中发生错误: {e}")
        return False





def attempt_signup_with_retry(driver):
    """
    执行注册的最后步骤，并包含重试逻辑。
    - 勾选复选框
    - 点击注册按钮
    - 如果失败，则根据错误信息进行重试或返回错误状态
    """
    max_retries = 5
    final_url = "https://app.augmentcode.com/account/subscription"
    for attempt in range(max_retries):
        print(f"\n第 {attempt + 1}/{max_retries} 次尝试注册...")
        try:
            # 步骤 5: 查找并勾选复选框，然后点击注册按钮
            print("步骤 5: 正在勾选复选框并点击注册按钮...")

            if driver.current_url == final_url:
                print("成功！当前在订阅页面。")
                return "SUCCESS"

            # 等待页面跳转后的结果
            time.sleep(3) # 等待一下，让页面有时间响应

            # 步骤 6: 检查是否出现 "Sign-up rejected"
            try:
                rejection_element = driver.find_element(By.XPATH, "/html/body/div/div/div/div")
                if "Sign-up rejected" in rejection_element.text:
                    print(f"检测到 'Sign-up rejected'。等待30秒后重试。")
                    return "RESTART"

                # 步骤 7: 检查是否出现 "Oops!" 错误
                if "Oops!, something went wrong" in driver.page_source:
                    print("检测到 'Oops!, something went wrong' 错误。")
                    return "RESTART" # 返回一个特殊信号，表示需要从头开始

                # 随机刷新


                driver.refresh()
                sleep_time = random.uniform(3, 5)
                print(f"   等待 {sleep_time:.2f} 秒...")
                time.sleep(random.uniform(7, 10))

                print("刷新完成，继续下一次尝试。")
                continue # 继续 for 循环的下一次迭代

            except NoSuchElementException:
                # 如果找不到拒绝元素，说明可能成功了，跳出循环进行URL检查
                print("未检测到 'Sign-up rejected'。")
                pass

            # 步骤 8: 检查最终的 URL
            print("步骤 8: 检查当前 URL 是否为订阅页面...")

            # 等待URL变为目标URL，最多等待10秒
            WebDriverWait(driver, 10).until(EC.url_to_be(final_url))

            if driver.current_url == final_url:
                print("成功！已跳转到订阅页面。")
                return "SUCCESS"
            else:
                # 如果URL不匹配，但也没有拒绝信息，可能是一个未知的状态
                print(f"警告：当前 URL 为 {driver.current_url}，与预期不符。")
                # 这种情况也视为一次失败的尝试
                driver.back() # 尝试回退以进行下一次重试
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )


        except TimeoutException as e:
            print(f"在尝试注册时发生超时错误: {e}")
            logger.error(f"在尝试注册时发生超时错误: {e}")
            #logger.error(driver.page_source)
            # 超时也可能意味着需要重试，先回退
            return "RESTART"
        except Exception as e:
            print(f"在尝试注册时发生未知错误: {e}")
            logger.error(f"在尝试注册时发生未知错误: {e}")
            #logger.error(driver.page_source)
            return "RESTART" # 发生其他严重错误时，最好从头开始

    print("已达到最大重试次数，注册失败。")
    return "FAILURE"

def get_vscode(driver,email):
    data = get_codeinfo()
    print(f"代码信息: {json.dumps(data, indent=2)}")
    print("=" * 50)
    if data:
        params = {
            "response_type": "code",
            "code_challenge": data["code_challenge"],
            "code_challenge_method": "S256",
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult",
            "state": data["state"],
            "scope": "email",
            "prompt": "login"
        }
        query_str = "&".join([f"{k}={v}" for k, v in params.items()])
        driver.get("https://auth.augmentcode.com/terms-accept" + f"?{query_str}")
        #cookie = tab.cookies().as_dict().get("session", "")
        cookie = driver.get_cookie("session").get("value","")
        res = driver.page_source
        # res = s.get(url="https://auth.augmentcode.com/terms-accept",params=params,cookies=cookie).text
        if "vscode://" in res:
            match = re.search(r'href="(vscode://[^"]+)"', res)
            if match:
                decoded_url = html.unescape(match.group(1))
                print("Extracted URL:", decoded_url)
                tokeninfo = get_augtoken(decoded_url, data["codeVerifier"])
                if tokeninfo:
                    accessToken = tokeninfo.get("accessToken", 0)
                    tenantURL = tokeninfo.get("tenantURL", 0)
                    end_date = tokeninfo.get("end_date", 0)
                    try:
                        ###smartdocker.online 565847.cfd
                        add_session(email.replace(DOMAIN,"smail.com"),{
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]
                            },expire_time=end_date,other=cookie)
                        print(f"添加session: {email}   {accessToken}   {tenantURL}  {end_date}")
                        print(f"email:{email} === cookie:{cookie}")
                    except Exception as e:
                        print("添加会话出错")
                        logging.info(accessToken,tenantURL)
                        print({
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]},expire_time=end_date,other=cookie)




def main():
    """主函数，控制整个自动化流程"""
    print("开始自动化注册流程...")

    # 获取邮箱信息
    email_jwt = get_email()
    if not email_jwt:
        print("获取邮箱失败")
        return "FAILURE"
    email, jwt = email_jwt
    print(f"获取到邮箱: {email}")

    driver = None
    while True:
        try:
            # 初始化浏览器
            print("正在初始化浏览器...")
            driver = setup_driver()

            # 应用反检测机制
            apply_anti_detection(driver)

            logger.info(f"开始处理邮箱: {email}")

            # 模拟启动后的人类行为
            time.sleep(random.uniform(2.0, 4.0))

            # 执行登录
            print("开始登录流程...")
            if not login(driver, email):
                raise Exception("登录步骤失败")

            # 登录后等待
            time.sleep(random.uniform(3.0, 5.0))

            # 获取邮箱验证码
            print("正在获取邮箱验证码...")
            max_email_attempts = 10
            code = None
            for attempt in range(max_email_attempts):
                code = get_email_data(jwt)
                if code:
                    print(f"成功获取验证码: {code}")
                    break
                print(f"第 {attempt + 1} 次获取验证码失败，等待重试...")
                time.sleep(random.uniform(3.0, 5.0))

            if not code:
                raise Exception("无法获取邮箱验证码")

            # 执行邮箱验证
            print("开始邮箱验证流程...")
            if not verify_email(driver, code):
                raise Exception("邮箱验证步骤失败")

            # 验证后等待
            time.sleep(random.uniform(3.0, 5.0))

            # 进入最关键的注册和重试步骤
            print("开始注册流程...")
            result = attempt_signup_with_retry(driver)

            if result == "SUCCESS":
                print("\n✅ 进入订阅页面，开始获取vscode_url")
                try:
                    # 成功后的人性化停顿
                    time.sleep(random.uniform(3.0, 5.0))
                    get_vscode(driver, email)
                    print("\n🎉 自动化流程成功完成！")
                    break # 成功，跳出 while 循环
                except Exception as e:
                    print(f"获取vscode信息时出错: {e}")
                    # 尝试备用方案
                    try:
                        get_augtoken(driver, email)
                    except Exception as e2:
                        print(f"备用方案也失败: {e2}")
                    break  # 即使失败也退出，因为主要流程已完成

            elif result == "RESTART":
                print("\n⚠️ 检测到严重错误，将关闭浏览器并重新开始整个流程...")
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = None
                time.sleep(random.uniform(5.0, 10.0))  # 重启前等待
                continue # 会自动进入下一次 while 循环

            else: # result == "FAILURE"
                print("\n❌ 所有重试均告失败，流程终止。")
                break # 失败，跳出 while 循环

        except Exception as e:
            print(f"\n💥 主流程中发生严重错误: {e}")
            logger.error(f"主流程错误: {e}")
            print("准备重启流程...")

            # 清理浏览器
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None

            # 重启前等待
            time.sleep(random.uniform(5.0, 10.0))

        finally:
            # 确保浏览器被正确关闭
            if driver:
                try:
                    driver.quit()
                except:
                    pass

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    n = 0
    ALLDOMAIN = {
        "slogo.eu.org":"slogo.com"
    }
    #while True:
    DOMAIN = random.choice(list(ALLDOMAIN.keys()))
    while True:
        try:
            main()
            n+=1
            print(f"添加第{n}个")
            #time.sleep(random.uniform(100,200))
            time.sleep(random.uniform(10,20))
        except Exception as e:
            print(f"  报错 \n{e}")
        # if n >= 5:
        #     break
