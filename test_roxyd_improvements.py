#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RoxyD 反检测改进测试脚本
用于验证反检测机制是否正常工作
"""

import os
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from RoxyClient import RoxyClient

def test_anti_detection():
    """测试反检测机制"""
    print("开始测试反检测机制...")
    
    # 设置环境变量（如果没有的话）
    if not os.getenv("ROXYBRWOSER_ID"):
        print("警告: 未设置 ROXYBRWOSER_ID 环境变量")
        return False
    
    if not os.getenv("ROXYWORK_ID"):
        print("警告: 未设置 ROXYWORK_ID 环境变量")
        return False
        
    if not os.getenv("ROXYTOKEN"):
        print("警告: 未设置 ROXYTOKEN 环境变量")
        return False
    
    try:
        # 导入改进后的函数
        from RoxyD import setup_driver, setup_cdp_anti_detection, enhance_anti_detection
        
        # 初始化浏览器
        print("正在初始化浏览器...")
        driver = setup_driver()
        
        # 测试基本页面访问
        print("测试页面访问...")
        driver.get("https://bot.sannysoft.com/")
        time.sleep(3)
        
        # 检查反检测效果
        print("检查反检测效果...")
        
        # 检查 webdriver 属性
        webdriver_detected = driver.execute_script("return navigator.webdriver")
        print(f"navigator.webdriver: {webdriver_detected}")
        
        # 检查语言设置
        languages = driver.execute_script("return navigator.languages")
        print(f"navigator.languages: {languages}")
        
        # 检查硬件信息
        hardware_concurrency = driver.execute_script("return navigator.hardwareConcurrency")
        print(f"navigator.hardwareConcurrency: {hardware_concurrency}")
        
        # 检查设备内存
        device_memory = driver.execute_script("return navigator.deviceMemory")
        print(f"navigator.deviceMemory: {device_memory}")
        
        # 检查平台信息
        platform = driver.execute_script("return navigator.platform")
        print(f"navigator.platform: {platform}")
        
        # 检查插件信息
        plugins_length = driver.execute_script("return navigator.plugins.length")
        print(f"navigator.plugins.length: {plugins_length}")
        
        # 检查自动化相关变量
        cdc_vars = driver.execute_script("""
            var cdcVars = [];
            for (var prop in window) {
                if (prop.includes('cdc_')) {
                    cdcVars.push(prop);
                }
            }
            return cdcVars;
        """)
        print(f"CDC 变量: {cdc_vars}")
        
        # 检查 chrome 对象
        chrome_exists = driver.execute_script("return typeof window.chrome !== 'undefined'")
        print(f"Chrome 对象存在: {chrome_exists}")
        
        # 等待用户查看结果
        print("\n请查看浏览器页面，检查反检测效果...")
        print("页面应该显示大部分检测项为绿色（通过）")
        input("按回车键继续...")
        
        # 测试 Augment Code 登录页面
        print("\n测试 Augment Code 登录页面...")
        driver.get("https://app.augmentcode.com/account")
        time.sleep(5)
        
        # 检查页面是否正常加载
        page_title = driver.title
        print(f"页面标题: {page_title}")
        
        # 检查是否有错误信息
        page_source = driver.page_source
        if "blocked" in page_source.lower() or "forbidden" in page_source.lower():
            print("⚠️ 页面可能被阻止访问")
        else:
            print("✅ 页面正常加载")
        
        print("\n测试完成！")
        input("按回车键关闭浏览器...")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

def test_human_behavior():
    """测试人类行为模拟"""
    print("开始测试人类行为模拟...")
    
    try:
        from RoxyD import setup_driver, simulate_human_behavior, human_like_typing, human_like_mouse_move_and_click
        from selenium.webdriver.common.by import By
        
        driver = setup_driver()
        
        # 访问测试页面
        driver.get("https://www.google.com")
        time.sleep(2)
        
        # 测试人类行为模拟
        print("测试页面行为模拟...")
        simulate_human_behavior(driver)
        
        # 测试输入模拟
        try:
            search_box = driver.find_element(By.NAME, "q")
            print("测试人类化输入...")
            human_like_typing(search_box, "selenium automation test")
            time.sleep(2)
        except Exception as e:
            print(f"输入测试跳过: {e}")
        
        print("人类行为测试完成！")
        input("按回车键关闭浏览器...")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"人类行为测试过程中发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("RoxyD 反检测改进测试")
    print("=" * 50)
    
    # 测试反检测机制
    print("\n1. 测试反检测机制")
    if test_anti_detection():
        print("✅ 反检测机制测试通过")
    else:
        print("❌ 反检测机制测试失败")
    
    time.sleep(2)
    
    # 测试人类行为模拟
    print("\n2. 测试人类行为模拟")
    if test_human_behavior():
        print("✅ 人类行为模拟测试通过")
    else:
        print("❌ 人类行为模拟测试失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("提示: 建议安装 python-dotenv 来加载环境变量")
    
    main()
